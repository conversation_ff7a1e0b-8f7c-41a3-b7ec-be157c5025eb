{"version": 1, "dependencies": {"net9.0-windows7.0": {"DalamudPackager": {"type": "Direct", "requested": "[13.0.0, )", "resolved": "13.0.0", "contentHash": "Mb3cUDSK/vDPQ8gQIeuCw03EMYrej1B4J44a1AvIJ9C759p9XeqdU9Hg4WgOmlnlPe0G7ILTD32PKSUpkQNa8w=="}, "DotNet.ReproducibleBuilds": {"type": "Direct", "requested": "[1.2.25, )", "resolved": "1.2.25", "contentHash": "xCXiw7BCxHJ8pF6wPepRUddlh2dlQlbr81gXA72hdk4FLHkKXas7EH/n+fk5UCA/YfMqG1Z6XaPiUjDbUNBUzg=="}, "Downloader": {"type": "Direct", "requested": "[3.3.4, )", "resolved": "3.3.4", "contentHash": "/M/c80e1L0WW1XrLSSiQhgFxk8rrfbpWiWDn2CeBg1tPD393Neo+v184yG/ThyhE9rrNp36yCrugiCmEbRf+VQ==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.1"}}, "Glamourer.Api": {"type": "Direct", "requested": "[2.4.0, )", "resolved": "2.4.0", "contentHash": "i8PEKb4R2tyunfHEHZHY+yCx2p6D0hsoQ24Q8UDVb7rNvynzDqMrRhGTQjuF+Tg2XGWA7sb32wJqHXCv0oKoeA=="}, "K4os.Compression.LZ4.Legacy": {"type": "Direct", "requested": "[1.3.8, )", "resolved": "1.3.8", "contentHash": "+82CK5zXbGjMnVLm2JJpsSAz3+4CRcMmxUDOmehUh1bDjrmpnc5VHxB14hiV1lXtqe53lNI+O76BQvgWhgI66g==", "dependencies": {"K4os.Compression.LZ4": "1.3.8"}}, "Meziantou.Analyzer": {"type": "Direct", "requested": "[2.0.189, )", "resolved": "2.0.189", "contentHash": "/e+dh95vDdvCTbViV2cWpXJEXAj+VHq7FsBXCTTTsLcffV0bkgXDFAPY0zMpy+Vt91Cl2cBoSOfaAoSdtn796Q=="}, "Microsoft.AspNetCore.SignalR.Client": {"type": "Direct", "requested": "[9.0.3, )", "resolved": "9.0.3", "contentHash": "V8K94AN9ADbpP2jxwt8Y++g7t/XZ7oEV+GZizNvLnR8dpCYWeveIZ/tItO54jfZJ5jmt5YyideOc+ErZbr1IZg==", "dependencies": {"Microsoft.AspNetCore.Http.Connections.Client": "9.0.3", "Microsoft.AspNetCore.SignalR.Client.Core": "9.0.3"}}, "Microsoft.AspNetCore.SignalR.Protocols.MessagePack": {"type": "Direct", "requested": "[9.0.3, )", "resolved": "9.0.3", "contentHash": "mMQ21T4NuqGrX1UzSe1WBmg6TUlOmpMgCoA9kAy/uBWBZlAA4+NFavbCULyJy6zTSUAvZkG3cGSnQN4dLJlF/w==", "dependencies": {"MessagePack": "2.5.187", "Microsoft.AspNetCore.SignalR.Common": "9.0.3"}}, "Microsoft.Extensions.Hosting": {"type": "Direct", "requested": "[9.0.3, )", "resolved": "9.0.3", "contentHash": "ioFXglqFA9uCYcKHI3CLVTO3I75jWIhvVxiZBzGeSPxw7XdhDLh0QvbNFrMTbZk9qqEVQcylblcvcNXnFHYXyA==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.Binder": "9.0.3", "Microsoft.Extensions.Configuration.CommandLine": "9.0.3", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.3", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.3", "Microsoft.Extensions.Configuration.Json": "9.0.3", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.3", "Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Diagnostics": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Physical": "9.0.3", "Microsoft.Extensions.Hosting.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Configuration": "9.0.3", "Microsoft.Extensions.Logging.Console": "9.0.3", "Microsoft.Extensions.Logging.Debug": "9.0.3", "Microsoft.Extensions.Logging.EventLog": "9.0.3", "Microsoft.Extensions.Logging.EventSource": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}}, "NReco.Logging.File": {"type": "Direct", "requested": "[1.2.2, )", "resolved": "1.2.2", "contentHash": "UyUIkyDiHi2HAJlmEWqeKN9/FxTF0DPNdyatzMDMTXvUpgvqBFneJ2qDtZkXRJNG8eR6jU+KsbGeMmChgUdRUg==", "dependencies": {"Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Configuration": "8.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Penumbra.Api": {"type": "Direct", "requested": "[5.6.0, )", "resolved": "5.6.0", "contentHash": "zMmkxX1+7COn23aTeq0L+UMA79ocmvQdvj5RgksEfTKrKZLbtaqRPCe6NsChyfGvcj79sgfAW/9hJ+FAzzVcNg=="}, "Penumbra.String": {"type": "Direct", "requested": "[1.0.5, )", "resolved": "1.0.5", "contentHash": "+9YRQxwkzW6Ys/hx8vHvTwYV76QMjbf7Puq5SibxVLNzkPLyKLp7qZCKS1SC4yXPJlPB4g80IqxrxCm0yacMFw=="}, "SixLabors.ImageSharp": {"type": "Direct", "requested": "[3.1.7, )", "resolved": "3.1.7", "contentHash": "9fIOOAsyLFid6qKypM2Iy0Z3Q9yoanV8VoYAHtI2sYGMNKzhvRTjgFDHonIiVe+ANtxIxM6SuqUzj0r91nItpA=="}, "SonarAnalyzer.CSharp": {"type": "Direct", "requested": "[10.7.0.110445, )", "resolved": "10.7.0.110445", "contentHash": "U4v2LWopxADYkUv7Z5CX7ifKMdDVqHb7a1bzppIQnQi4WQR6z1Zi5rDkCHlVYGEd1U/WMz1IJCU8OmFZLJpVig=="}, "System.IdentityModel.Tokens.Jwt": {"type": "Direct", "requested": "[8.7.0, )", "resolved": "8.7.0", "contentHash": "8dKL3A9pVqYCJIXHd4H2epQqLxSvKeNxGonR0e5g89yMchyvsM/NLuB06otx29BicUd6+LUJZgNZmvYjjPsPGg==", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.7.0", "Microsoft.IdentityModel.Tokens": "8.7.0"}}, "K4os.Compression.LZ4": {"type": "Transitive", "resolved": "1.3.8", "contentHash": "LhwlPa7c1zs1OV2XadMtAWdImjLIsqFJPoRcIWAadSRn0Ri1DepK65UbWLPmt4riLqx2d40xjXRk0ogpqNtK7g=="}, "MessagePack": {"type": "Transitive", "resolved": "2.5.187", "contentHash": "uW4j8m4Nc+2Mk5n6arOChavJ9bLjkis0qWASOj2h2OwmfINuzYv+mjCHUymrYhmyyKTu3N+ObtTXAY4uQ7jIhg==", "dependencies": {"MessagePack.Annotations": "2.5.187", "Microsoft.NET.StringTools": "17.6.3"}}, "MessagePack.Annotations": {"type": "Transitive", "resolved": "3.1.3", "contentHash": "XTy4njgTAf6UVBKFj7c7ad5R0WVKbvAgkbYZy4f00kplzX2T3VOQ34AUke/Vn/QgQZ7ETdd34/IDWS3KBInSGA=="}, "Microsoft.AspNetCore.Connections.Abstractions": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "MWkNy/Yhv2q5ZVYPHjHN6pEE5Ya1r4opqSpnsW60bgpDOT54zZ6Kpqub4Tcat8ENsR5PZcTZ3eeSAthweUb/KA==", "dependencies": {"Microsoft.Extensions.Features": "9.0.3"}}, "Microsoft.AspNetCore.Http.Connections.Client": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "bLoLX67FBeYK1KKGfXrmBki/F9EAK8EKCNkyADtfFjQkJ1Qhhw1sjBlcL8TbVnZxk+FaFsyCeBPmSHgOwNIJ/A==", "dependencies": {"Microsoft.AspNetCore.Http.Connections.Common": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "System.Net.ServerSentEvents": "9.0.3"}}, "Microsoft.AspNetCore.Http.Connections.Common": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "GYDAXEmaG/q9UgixPchsLAVbBUbdgG3hd8J7Af4k4GIKLsibAhos7QY7hHicyULJvRtl03totiRi5Z+JIKEnUA==", "dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "9.0.3"}}, "Microsoft.AspNetCore.SignalR.Client.Core": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "R2N03AK5FH8KIENfJGER4SgjJFMJTBiYuLbovbRunp5R4knO+iysfbYMfEFO3kn98ElWr/747dS4AeWQOEEQsg==", "dependencies": {"Microsoft.AspNetCore.SignalR.Common": "9.0.3", "Microsoft.AspNetCore.SignalR.Protocols.Json": "9.0.3", "Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "System.Threading.Channels": "9.0.3"}}, "Microsoft.AspNetCore.SignalR.Common": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "/568tq8YVas1mDgeScmQdQV4ZDRjdyqDS3rAo17R5Bs4puMaNM80wQSwcvsmN5gSwH6L/XRTmD1J1uRIyKXrCg==", "dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}}, "Microsoft.AspNetCore.SignalR.Protocols.Json": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "jvOdsquqrbWMP3/Aq4s8/yVeCxBkjvxarv/2WgubKkQT8nZ46aKY3Rvj1uolp4N3TuaMGlnd6mhK/tF7jCat2Q==", "dependencies": {"Microsoft.AspNetCore.SignalR.Common": "9.0.3"}}, "Microsoft.Extensions.Configuration": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "RIEeZxWYm77+OWLwgik7DzSVSONjqkmcbuCb1koZdGAV7BgOUWnLz80VMyHZMw3onrVwFCCMHBBdruBPuQTvkg==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}}, "Microsoft.Extensions.Configuration.Abstractions": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "q5qlbm6GRUrle2ZZxy9aqS/wWoc+mRD3JeP6rcpiJTh5XcemYkplAcJKq8lU11ZfPom5lfbZZfnQvDqcUhqD5Q==", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}}, "Microsoft.Extensions.Configuration.Binder": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "ad82pYBUSQbd3WIboxsS1HzFdRuHKRa2CpYwie/o6dZAxUjt62yFwjoVdM7Iw2VO5fHV1rJwa7jJZBNZin0E7Q==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.3"}}, "Microsoft.Extensions.Configuration.CommandLine": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "rVwz4ml/Jve/QzzUlyTVOKXVZ37op9RK6Ize4uPmJ3S5c2ErExoy816+dslBQ06ZrFq8M9bpnV5LVBuPD1ONHQ==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "fo84UIa8aSBG3pOtzLsgkj1YkOVfYFy2YWcRTCevHHAkuVsxnYnKBrcW2pyFgqqfQ/rT8K1nmRXHDdQIZ8PDig==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3"}}, "Microsoft.Extensions.Configuration.FileExtensions": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "tBNMSDJ2q7WQK2zwPhHY5I/q95t7sf6dT079mGrNm0yOZF/gM9JvR/LtCb/rwhRmh7A6XMnzv5WbpCh9KLq9EQ==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Physical": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}}, "Microsoft.Extensions.Configuration.Json": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "mjkp3ZwynNacZk4uq93I0DyCY48FZmi3yRV0xlfeDuWh44KcDunPXHwt8IWr4kL7cVM6eiFVe6YTJg97KzUAUA==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3"}}, "Microsoft.Extensions.Configuration.UserSecrets": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "vwkBQ5jqmfX7nD7CFvB3k1uSeNBKRcYRDvlk3pxJzJfm/cgT4R+hQg5AFXW/1aLKjz0q7brpRocHC5GK2sjvEw==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.Json": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Physical": "9.0.3"}}, "Microsoft.Extensions.DependencyInjection": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "lDbxJpkl6X8KZGpkAxgrrthQ42YeiR0xjPp7KPx+sCPc3ZbpaIbjzd0QQ+9kDdK2RU2DOl3pc6tQyAgEZY3V0A==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3"}}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "TfaHPSe39NyL2wxkisRxXK7xvHGZYBZ+dy3r+mqGvnxKgAPdHkMu3QMQZI4pquP6W5FIQBqs8FJpWV8ffCgDqQ=="}, "Microsoft.Extensions.Diagnostics": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "gqhbIq6adm0+/9IlDYmchekoxNkmUTm7rfTG3k4zzoQkjRuD8TQGwL1WnIcTDt4aQ+j+Vu0OQrjI8GlpJQQhIA==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.3", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.3"}}, "Microsoft.Extensions.Diagnostics.Abstractions": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "/fn0Xe8t+3YbMfwyTk4hFirWyAG1pBA5ogVYsrKAuuD2gbqOWhFuSA28auCmS3z8Y2eq3miDIKq4pFVRWA+J6g==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}}, "Microsoft.Extensions.Features": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "jZuO3APLh0ePwtT9PDxiMdPwpDdct/kuExlXLCZZ+XFje/Xt815MM827EFJuxTBAbL148ywyfJyjIZ92osP5WA=="}, "Microsoft.Extensions.FileProviders.Abstractions": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "umczZ3+QPpzlrW/lkvy+IB0p52+qZ5w++aqx2lTCMOaPKzwcbVdrJgiQ3ajw5QWBp7gChLUiCYkSlWUpfjv24g==", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}}, "Microsoft.Extensions.FileProviders.Physical": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "th2+tQBV5oWjgKhip9GjiIv2AEK3QvfAO3tZcqV3F3dEt5D6Gb411RntCj1+8GS9HaRRSxjSGx/fCrMqIjkb1Q==", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.FileSystemGlobbing": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}}, "Microsoft.Extensions.FileSystemGlobbing": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "Rec77KHk4iNpFznHi5/6wF3MlUDcKqg26t8gRYbUm1PSukZ4B6mrXpZsJSNOiwyhhQVkjYbaoZxi5XJgRQ5lFg=="}, "Microsoft.Extensions.Hosting.Abstractions": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "rHabYVhQsGYNfgnfnYLqZRx/hLe85i6jW5rnDjA9pjt3x7yjPv8T/EXcgN5T9T38FAVwZRA+RMGUkEHbxvCOBQ==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3"}}, "Microsoft.Extensions.Logging": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "utIi2R1nm+PCWkvWBf1Ou6LWqg9iLfHU23r8yyU9VCvda4dEs7xbTZSwGa5KuwbpzpgCbHCIuKaFHB3zyFmnGw==", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}}, "Microsoft.Extensions.Logging.Abstractions": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "H/MBMLt9A/69Ux4OrV7oCKt3DcMT04o5SCqDolulzQA66TLFEpYYb4qedMs/uwrLtyHXGuDGWKZse/oa8W9AZw==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3"}}, "Microsoft.Extensions.Logging.Configuration": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "eVZsaKNyK0g0C1qp0mmn4Q2PiX+bXdkz8+zVkXyVMk8IvoWfmTjLjEq1MQlwt1A22lToANPiUrxPJ7Tt3V5puw==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.Binder": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.3"}}, "Microsoft.Extensions.Logging.Console": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "o9VXLOdpTAro1q7ZThIB3S8OHrRn5pr8cFUCiN85fiwlfAt2DhU4ZIfHy+jCNbf7y7S5Exbr3dlDE8mKNrs0Yg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Configuration": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}}, "Microsoft.Extensions.Logging.Debug": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "BlKgvNYjD6mY5GXpMCf9zPAsrovMgW5mzCOT7SpoOSyI1478zldf+7PKvDIscC277z5zjSO3yi/OuIWpnTZmdA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3"}}, "Microsoft.Extensions.Logging.EventLog": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "/+elZUHGgB3oHKO9St/Ql/qfze9O+UbXj+9FOj1gIshLCFXcPlhpKoI11jE6eIV0kbs1P/EeffJl4KDFyvAiJQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "System.Diagnostics.EventLog": "9.0.3"}}, "Microsoft.Extensions.Logging.EventSource": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "hgG0EGEHnngQFQNqJ5ungEykqaQ5Tik0Gpkb38pea2a5cR3pWlZR4vuYLDdtTgSiKEKByXz/3wNQ7qAqXamEEA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}}, "Microsoft.Extensions.Options": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "xE7MpY70lkw1oiid5y6FbL9dVw8oLfkx8RhSNGN8sSzBlCqGn0SyT3Fqc8tZnDaPIq7Z8R9RTKlS564DS+MV3g==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "PcyYHQglKnWVZHSPaL6v2qnfsIuFw8tSq7cyXHg3OeuDVn/CqmdWUjRiZomCF/Gi+qCi+ksz0lFphg2cNvB8zQ==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.Binder": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}}, "Microsoft.Extensions.Primitives": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "yCCJHvBcRyqapMSNzP+kTc57Eaavq2cr5Tmuil6/XVnipQf5xmskxakSQ1enU6S4+fNg3sJ27WcInV64q24JsA=="}, "Microsoft.IdentityModel.Abstractions": {"type": "Transitive", "resolved": "8.7.0", "contentHash": "OQd5aVepYvh5evOmBMeAYjMIpEcTf1ZCBZaU7Nh/RlhhdXefjFDJeP1L2F2zeNT1unFr+wUu/h3Ac2Xb4BXU6w=="}, "Microsoft.IdentityModel.JsonWebTokens": {"type": "Transitive", "resolved": "8.7.0", "contentHash": "uzsSAWhNhbrkWbQKBTE8QhzviU6sr3bJ1Bkv7gERlhswfSKOp7HsxTRLTPBpx/whQ/GRRHEwMg8leRIPbMrOgw==", "dependencies": {"Microsoft.IdentityModel.Tokens": "8.7.0"}}, "Microsoft.IdentityModel.Logging": {"type": "Transitive", "resolved": "8.7.0", "contentHash": "Bs0TznPAu+nxa9rAVHJ+j3CYECHJkT3tG8AyBfhFYlT5ldsDhoxFT7J+PKxJHLf+ayqWfvDZHHc4639W2FQCxA==", "dependencies": {"Microsoft.IdentityModel.Abstractions": "8.7.0"}}, "Microsoft.IdentityModel.Tokens": {"type": "Transitive", "resolved": "8.7.0", "contentHash": "5Z6voXjRXAnGklhmZd1mKz89UhcF5ZQQZaZc2iKrOuL4Li1UihG2vlJx8IbiFAOIxy/xdbsAm0A+WZEaH5fxng==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.IdentityModel.Logging": "8.7.0"}}, "Microsoft.NET.StringTools": {"type": "Transitive", "resolved": "17.6.3", "contentHash": "N0ZIanl1QCgvUumEL1laasU0a7sOE5ZwLZVTn0pAePnfhq8P7SvTjF8Axq+CnavuQkmdQpGNXQ1efZtu5kDFbA=="}, "System.Diagnostics.EventLog": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "0nDJBZ06DVdTG2vvCZ4XjazLVaFawdT0pnji23ISX8I8fEOlRJyzH2I0kWiAbCtFwry2Zir4qE4l/GStLATfFw=="}, "System.Net.ServerSentEvents": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "Vs/C2V27bjtwLqYag9ATzHilcUn8VQTICre4jSBMGFUeSTxEZffTjb+xZwjcmPsVAjmSZmBI5N7Ezq8UFvqQQg=="}, "System.Threading.Channels": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "Ao0iegVONKYVw0eWxJv0ArtMVfkFjgyyYKtUXru6xX5H95flSZWW3QCavD4PAgwpc0ETP38kGHaYbPzSE7sw2w=="}, "maresynchronos.api": {"type": "Project", "dependencies": {"MessagePack.Annotations": "[3.1.3, )"}}}}}