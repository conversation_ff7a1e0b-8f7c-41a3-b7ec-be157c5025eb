services:
  postgres:
    image: postgres:latest
    command: ["postgres", "-c", "log_statement=all"]
    restart: always
    ports:
      - 5432:5432/tcp
    environment:
      POSTGRES_DB: mare
      POSTGRES_USER: mare
      POSTGRES_PASSWORD: secretdevpassword
    volumes:
      - ../data/postgresql/:/var/lib/postgresql/data
      - postgres_socket:/var/run/postgresql:rw
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mare"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:latest
    command: [sh, -c, "rm -f /data/dump.rdb && redis-server --save \"\" --appendonly no --requirepass secretredispassword"]
    volumes:
      - cache:/data

  mare-server:
    image: darkarchon/mare-synchronos-server:latest
    restart: on-failure
    ports:
      - 6000:6000/tcp
      - 6050:6050/tcp
    environment:
      MareSynchronos__CdnFullUrl: "${DEV_MARE_CDNURL}"
      MareSynchronos__XIVAPIKey: "${DEV_MARE_XIVAPIKEY}"
      DOTNET_USE_POLLING_FILE_WATCHER: 1
    volumes:
      - ../config/standalone/server-standalone.json:/opt/MareSynchronosServer/appsettings.json
      - ../log/server-standalone/:/opt/MareSynchronosServer/logs/:rw
      - postgres_socket:/var/run/postgresql/:rw
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl --fail http://localhost:6000/health || exit 1"]
      retries: 60
      start_period: 10s
      timeout: 1s

  mare-auth:
    image: darkarchon/mare-synchronos-authservice:latest
    restart: on-failure
    environment:
      DOTNET_USE_POLLING_FILE_WATCHER: 1
    volumes:
      - ../config/standalone/authservice-standalone.json:/opt/MareSynchronosAuthService/appsettings.json
      - ../log/authservice-standalone/:/opt/MareSynchronosAuthService/logs/:rw
      - postgres_socket:/var/run/postgresql/:rw
    depends_on:
      mare-server:
        condition: service_healthy
      postgres:
        condition: service_healthy

  mare-services:
    image: darkarchon/mare-synchronos-services:latest
    restart: on-failure
    environment:
      MareSynchronos__DiscordBotToken: "${DEV_MARE_DISCORDTOKEN}"
      MareSynchronos__DiscordChannelForMessages: "${DEV_MARE_DISCORDCHANNEL}"
      MareSynchronos__DiscordChannelForReports: "${DEV_MARE_DISCORDCHANNEL}"
      MareSynchronos__DiscordChannelForCommands: "${DEV_MARE_DISCORDCHANNEL}"
      DOTNET_USE_POLLING_FILE_WATCHER: 1
    volumes:
      - ../config/standalone/services-standalone.json:/opt/MareSynchronosServices/appsettings.json
      - ../log/services-standalone/:/opt/MareSynchronosServices/logs/:rw
      - postgres_socket:/var/run/postgresql/:rw
    depends_on:
      mare-server:
        condition: service_healthy
      postgres:
        condition: service_healthy

  mare-files:
    image: darkarchon/mare-synchronos-staticfilesserver:latest
    ports:
      - 6200:6200/tcp
    restart: on-failure
    environment:
      MareSynchronos__CdnFullUrl: "${DEV_MARE_CDNURL}"
      DOTNET_USE_POLLING_FILE_WATCHER: 1
    volumes:
      - ../config/standalone/files-standalone.json:/opt/MareSynchronosStaticFilesServer/appsettings.json
      - ../log/files-standalone/:/opt/MareSynchronosStaticFilesServer/logs/:rw
      - postgres_socket:/var/run/postgresql/:rw
      - ../data/files-standalone/:/marecache/:rw
    depends_on:
      postgres:
        condition: service_healthy
      mare-server:
        condition: service_healthy

volumes:
  postgres_socket:
  cache:
    driver: local