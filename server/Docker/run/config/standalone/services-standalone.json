{"ConnectionStrings": {"DefaultConnection": "Host=/var/run/postgresql;Port=5432;Database=mare;Username=mare;Keepalive=15;Minimum Pool Size=10;Maximum Pool Size=50;No Reset On Close=true;Max Auto Prepare=50;Enlist=false"}, "Logging": {"LogLevel": {"Default": "Warning", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "MareSynchronosServices": "Information", "MareSynchronosShared": "Information", "System.IO": "Information"}, "File": {"BasePath": "logs", "FileAccessMode": "KeepOpenAndAutoFlush", "FileEncodingName": "utf-8", "DateFormat": "yyyMMdd", "MaxFileSize": 104857600, "Files": [{"Path": "<date:yyyy>/<date:MM>/<date:dd>/mare-<date:HH>-<counter:0000>.log"}]}}, "MareSynchronos": {"DbContextPoolSize": 512, "ShardName": "Services", "MetricsPort": 6150, "CdnFullUrl": "http://localhost:6200/", "MainServerAddress": "http://mare-server:6000/", "MainServerGrpcAddress": "http://mare-server:6005/", "DiscordBotToken": "", "DiscordChannelForMessages": "", "DiscordChannelForCommands": "", "Jwt": "teststringteststringteststringteststringteststringteststringteststringteststringteststringteststring", "RedisConnectionString": "redis,password=secretredispassword", "VanityRoles": {"984484868241096724": "Main Developer", "1012008556259725398": "Patreon Subscriber"}}, "AllowedHosts": "*", "Kestrel": {}, "IpRateLimiting": {}, "IPRateLimitPolicies": {}}