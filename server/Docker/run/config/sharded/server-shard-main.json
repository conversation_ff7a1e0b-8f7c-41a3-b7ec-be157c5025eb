{"ConnectionStrings": {"DefaultConnection": "Host=/var/run/postgresql;Port=5432;Database=mare;Username=mare;Keepalive=15;Minimum Pool Size=10;Maximum Pool Size=50;No Reset On Close=true;Max Auto Prepare=50;Enlist=false"}, "Logging": {"LogLevel": {"Default": "Warning", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "MareSynchronosServer": "Information", "MareSynchronosShared": "Information", "System.IO": "Information"}, "File": {"BasePath": "logs", "FileAccessMode": "KeepOpenAndAutoFlush", "FileEncodingName": "utf-8", "DateFormat": "yyyMMdd", "MaxFileSize": 104857600, "Files": [{"Path": "<date:yyyy>/<date:MM>/<date:dd>/mare-<date:HH>-<counter:0000>.log"}]}}, "MareSynchronos": {"DbContextPoolSize": 512, "ShardName": "Main", "MetricsPort": 6050, "MainServerGrpcAddress": "", "FailedAuthForTempBan": 5, "TempBanDurationInMinutes": 5, "Jwt": "teststringteststringteststringteststringteststringteststringteststringteststringteststringteststring", "WhitelistedIps": [""], "RedisConnectionString": "redis,password=secretredispassword", "CdnFullUrl": "http://localhost:6200/", "StaticFileServiceAddress": "http://mare-files:6205", "MaxExistingGroupsByUser": 3, "MaxJoinedGroupsByUser": 6, "MaxGroupUserCount": 100, "PurgeUnusedAccounts": false, "PurgeUnusedAccountsPeriodInDays": 14, "CdnShardConfiguration": [{"FileMatch": "^[********]", "CdnFullUrl": ""}, {"FileMatch": "^[89ABCDEF]", "CdnFullUrl": ""}]}, "AllowedHosts": "*", "Kestrel": {"Endpoints": {"Http": {"Url": "http://+:6000"}, "Grpc": {"Protocols": "Http2", "Url": "http://+:6005"}}}, "IpRateLimiting": {}, "IPRateLimitPolicies": {}}