FROM mcr.microsoft.com/dotnet/sdk:8.0 as BUILD

COPY MareAPI /server/MareAPI
COPY MareSynchronosServer/MareSynchronosShared /server/MareSynchronosServer/MareSynchronosShared
COPY MareSynchronosServer/MareSynchronosAuthService /server/MareSynchronosServer/MareSynchronosAuthService

WORKDIR /server/MareSynchronosServer/MareSynchronosAuthService/

RUN dotnet publish \
        --configuration=Debug \
        --os=linux \
        --output=/build \
        MareSynchronosAuthService.csproj

FROM mcr.microsoft.com/dotnet/aspnet:8.0

RUN adduser \
        --disabled-password \
        --group \
        --no-create-home \
        --quiet \
        --system \
        mare

COPY --from=BUILD /build /opt/MareSynchronosAuthService
RUN chown -R mare:mare /opt/MareSynchronosAuthService
RUN apt-get update; apt-get install curl -y

USER mare:mare
WORKDIR /opt/MareSynchronosAuthService

CMD ["./MareSynchronosAuthService"]
