{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=mare;Username=postgres"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Protocols": "Http2",
        "Url": "http://+:5002"
      }
    }
  },
  "MareSynchronos": {
    "DbContextPoolSize": 1024,
    "DiscordBotToken": "",
    "DiscordChannelForMessages": "",
    "PurgeUnusedAccounts": true,
    "PurgeUnusedAccountsPeriodInDays": 14,
    "FailedAuthForTempBan": 5,
    "TempBanDurationInMinutes": 30,
  },
  "AllowedHosts": "*"
}
