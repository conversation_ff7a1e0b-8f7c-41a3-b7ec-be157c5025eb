{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=mare;Username=postgres"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Kestrel": {"Endpoints": {"Http": {"Url": "http://+:5001"}, "Gprc": {"Protocols": "Http2", "Url": "http://+:5003"}}}, "MareSynchronos": {"ForcedDeletionOfFilesAfterHours": -1, "CacheSizeHardLimitInGiB": -1, "UnusedFileRetentionPeriodInDays": 7, "CacheDirectory": "G:\\ServerTest", "ServiceAddress": "http://localhost:5002", "RemoteCacheSourceUri": ""}, "AllowedHosts": "*"}