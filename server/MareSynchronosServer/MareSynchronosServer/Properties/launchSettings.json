{
  "profiles": {
    "MareSynchronosServer": {
      "commandName": "Project",
      "dotnetRunMessages": "true",
      "launchBrowser": false,
      //"applicationUrl": "https://localhost:5001;http://localhost:5000;https://*************:5001;http://*************:5000",
      "applicationUrl": "http://localhost:5000;https://localhost:5001;https://darkarchon.internet-box.ch:5001",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}
