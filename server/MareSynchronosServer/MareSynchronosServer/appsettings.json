{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=mare;Username=postgres"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "MareSynchronosServer.Authentication": "Warning",
      "System.IO.IOException": "Warning"
    },
    "File": {
      "BasePath": "logs",
      "FileAccessMode": "KeepOpenAndAutoFlush",
      "FileEncodingName": "utf-8",
      "DateFormat": "yyyMMdd",
      "MaxFileSize": 10485760,
      "Files": [
        {
          "Path": "mare-<counter>.log"
        }
      ]
    }
  },
  "MareSynchronos": {
    "DbContextPoolSize": 2000,
    "CdnFullUrl": "https://<url or ip to your server>/cache/",
    "ServiceAddress": "http://localhost:5002",
    "StaticFileServiceAddress": "http://localhost:5003"
  },
  "AllowedHosts": "*",
  "Kestrel": {
    "Endpoints": {
      "Https": {
        "Url": "https://+:5000",
        "Certificate": {
          "Subject": "darkarchon.internet-box.ch",
          "Store": "My",
          "Location": "LocalMachine"
          //"AllowInvalid": false
          //          "Path": "", //use path, keypath and password to provide a valid certificate if not using windows key store
          //          "KeyPath": ""
          //          "Password": ""
        }
      }
    }
  },
  "IpRateLimiting": {
    "EnableEndpointRateLimiting": false,
    "StackBlockedRequests": false,
    "RealIpHeader": "X-Real-IP",
    "ClientIdHeader": "X-ClientId",
    "HttpStatusCode": 429,
    "IpWhitelist": [ ],
    "GeneralRules": [ ]
  },
  "IPRateLimitPolicies": {
    "IpRules": []
  }
}
